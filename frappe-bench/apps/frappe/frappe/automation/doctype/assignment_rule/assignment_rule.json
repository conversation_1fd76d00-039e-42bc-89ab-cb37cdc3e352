{"actions": [], "allow_rename": 1, "autoname": "Prompt", "creation": "2019-02-28 17:12:18.815830", "description": "Automatically Assign Documents to Users", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["document_type", "due_date_based_on", "priority", "disabled", "column_break_4", "description", "assignment_rules_section", "assign_condition", "column_break_6", "unassign_condition", "section_break_10", "close_condition", "sb", "assignment_days", "assign_to_users_section", "rule", "field", "users", "last_user"], "fields": [{"fieldname": "document_type", "fieldtype": "Link", "in_list_view": 1, "label": "Document Type", "options": "DocType", "reqd": 1}, {"description": "Higher priority rule will be applied first", "fieldname": "priority", "fieldtype": "Int", "label": "Priority"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"default": "Automatic Assignment", "description": "Example: {{ subject }}", "fieldname": "description", "fieldtype": "Small Text", "label": "Description", "reqd": 1}, {"fieldname": "assignment_rules_section", "fieldtype": "Section Break", "label": "Assignment Rules"}, {"description": "Simple Python Expression, Example: status == 'Open' and type == 'Bug'", "fieldname": "assign_condition", "fieldtype": "Code", "in_list_view": 1, "label": "Assign Condition", "options": "PythonExpression", "reqd": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"description": "Simple Python Expression, Example: Status in (\"Closed\", \"Cancelled\")", "fieldname": "unassign_condition", "fieldtype": "Code", "label": "Unassign Condition", "options": "PythonExpression"}, {"fieldname": "assign_to_users_section", "fieldtype": "Section Break", "label": "Assign To Users"}, {"fieldname": "rule", "fieldtype": "Select", "in_list_view": 1, "label": "Rule", "options": "Round Robin\nLoad Balancing\nBased on Field", "reqd": 1}, {"depends_on": "eval: doc.rule !== 'Based on Field'", "fieldname": "users", "fieldtype": "Table MultiSelect", "label": "Users", "mandatory_depends_on": "eval: doc.rule !== 'Based on Field'", "options": "Assignment Rule User"}, {"fieldname": "last_user", "fieldtype": "Link", "label": "Last User", "options": "User", "read_only": 1}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"description": "Simple Python Expression, Example: Status in (\"Invalid\")", "fieldname": "close_condition", "fieldtype": "Code", "label": "Close Condition", "options": "PythonExpression"}, {"fieldname": "sb", "fieldtype": "Section Break", "label": "Assignment Days"}, {"fieldname": "assignment_days", "fieldtype": "Table", "label": "Assignment Days", "options": "Assignment Rule Day", "reqd": 1}, {"depends_on": "document_type", "description": "Value from this field will be set as the due date in the ToDo", "fieldname": "due_date_based_on", "fieldtype": "Select", "label": "Due Date Based On"}, {"depends_on": "eval: doc.rule == 'Based on Field'", "fieldname": "field", "fieldtype": "Select", "label": "Field", "mandatory_depends_on": "eval: doc.rule == 'Based on Field'"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-03-23 16:01:27.590910", "modified_by": "Administrator", "module": "Automation", "name": "Assignment Rule", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
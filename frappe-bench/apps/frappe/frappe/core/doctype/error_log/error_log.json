{"actions": [], "creation": "2013-01-16 13:09:40", "doctype": "DocType", "document_type": "System", "engine": "MyISAM", "field_order": ["seen", "reference_doctype", "column_break_3", "reference_name", "section_break_5", "method", "error", "trace_id"], "fields": [{"default": "0", "fieldname": "seen", "fieldtype": "Check", "hidden": 1, "label": "Seen"}, {"fieldname": "method", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "read_only": 1}, {"fieldname": "error", "fieldtype": "Code", "label": "Error", "read_only": 1}, {"fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Reference DocType", "options": "DocType", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Reference Name", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"fieldname": "trace_id", "fieldtype": "Data", "label": "Trace ID", "read_only": 1}], "icon": "fa fa-warning-sign", "idx": 1, "in_create": 1, "links": [], "modified": "2024-12-09 14:22:44.819718", "modified_by": "Administrator", "module": "Core", "name": "<PERSON><PERSON><PERSON>", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "method"}
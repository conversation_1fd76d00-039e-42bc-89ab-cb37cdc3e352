{"actions": [], "creation": "2020-05-11 17:44:54.674657", "doctype": "DocType", "engine": "InnoDB", "field_order": ["app_name", "app_version", "git_branch", "has_setup_wizard", "is_setup_complete"], "fields": [{"columns": 2, "fieldname": "git_branch", "fieldtype": "Data", "in_list_view": 1, "label": "Git Branch", "read_only": 1, "reqd": 1}, {"columns": 2, "fieldname": "app_name", "fieldtype": "Data", "in_list_view": 1, "label": "Application Name", "read_only": 1, "reqd": 1}, {"columns": 2, "fieldname": "app_version", "fieldtype": "Data", "in_list_view": 1, "label": "Application Version", "read_only": 1, "reqd": 1}, {"columns": 2, "default": "0", "fieldname": "has_setup_wizard", "fieldtype": "Check", "in_list_view": 1, "label": "Has Setup Wizard"}, {"columns": 2, "default": "0", "fieldname": "is_setup_complete", "fieldtype": "Check", "in_list_view": 1, "label": "Is Setup Complete?"}], "grid_page_length": 50, "istable": 1, "links": [], "modified": "2025-05-22 12:26:49.523690", "modified_by": "Administrator", "module": "Core", "name": "Installed Application", "owner": "Administrator", "permissions": [], "quick_entry": 1, "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
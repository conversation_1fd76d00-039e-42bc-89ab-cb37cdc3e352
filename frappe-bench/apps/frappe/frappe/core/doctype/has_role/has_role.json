{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:27:34", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["role"], "fields": [{"fieldname": "role", "fieldtype": "Link", "in_list_view": 1, "label": "Role", "oldfieldname": "role", "oldfieldtype": "Link", "options": "Role"}], "idx": 1, "istable": 1, "links": [], "modified": "2024-03-23 16:03:27.291168", "modified_by": "Administrator", "module": "Core", "name": "Has Role", "owner": "Administrator", "permissions": [], "sort_field": "creation", "sort_order": "DESC", "states": []}
{"actions": [], "allow_rename": 1, "autoname": "field:role_name", "creation": "2013-01-08 15:50:01", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["role_name", "home_page", "restrict_to_domain", "column_break_4", "disabled", "is_custom", "desk_access", "two_factor_auth"], "fields": [{"fieldname": "role_name", "fieldtype": "Data", "label": "Role Name", "oldfieldname": "role_name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"default": "0", "description": "If disabled, this role will be removed from all users.", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"default": "1", "fieldname": "desk_access", "fieldtype": "Check", "in_list_view": 1, "label": "Desk Access"}, {"default": "0", "fieldname": "two_factor_auth", "fieldtype": "Check", "label": "Two Factor Authentication"}, {"fieldname": "restrict_to_domain", "fieldtype": "Link", "label": "Restrict To Domain", "options": "Domain"}, {"description": "Route: Example \"/app\"", "fieldname": "home_page", "fieldtype": "Data", "label": "Home Page"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_custom", "fieldtype": "Check", "in_list_view": 1, "label": "Is Custom"}], "icon": "fa fa-bookmark", "idx": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2024-09-19 17:07:08.672124", "modified_by": "Administrator", "module": "Core", "name": "Role", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1, "translated_doctype": 1}
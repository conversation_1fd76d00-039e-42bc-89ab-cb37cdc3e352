{"add_total_row": 0, "apply_user_permissions": 1, "disabled": 0, "docstatus": 0, "doctype": "Report", "is_standard": "No", "javascript": null, "json": "{\"filters\":[],\"columns\":[[\"name\",\"User\"],[\"user_type\",\"User\"],[\"first_name\",\"User\"],[\"last_name\",\"User\"],[\"last_active\",\"User\"],[\"role\",\"Has Role\"]],\"sort_by\":\"User.creation\",\"sort_order\":\"desc\",\"sort_by_next\":null,\"sort_order_next\":\"desc\"}", "modified": "2016-09-01 02:59:07.728890", "module": "Core", "name": "User Activity Report", "query": null, "ref_doctype": "User", "report_name": "User Activity Report", "report_type": "Report Builder"}
{"actions": [], "autoname": "hash", "creation": "2013-01-17 11:36:45", "description": "List of patches executed", "doctype": "DocType", "document_type": "System", "engine": "InnoDB", "field_order": ["patch", "skipped", "traceback"], "fields": [{"fieldname": "patch", "fieldtype": "Code", "label": "Patch", "read_only": 1}, {"default": "0", "fieldname": "skipped", "fieldtype": "Check", "in_list_view": 1, "label": "Skipped", "read_only": 1}, {"depends_on": "eval:doc.skipped == 1", "fieldname": "traceback", "fieldtype": "Code", "label": "<PERSON><PERSON>", "read_only": 1}], "icon": "fa fa-cog", "idx": 1, "links": [], "modified": "2024-03-23 16:03:34.014409", "modified_by": "Administrator", "module": "Core", "name": "<PERSON>", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"email": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator"}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager"}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "patch", "track_changes": 1}
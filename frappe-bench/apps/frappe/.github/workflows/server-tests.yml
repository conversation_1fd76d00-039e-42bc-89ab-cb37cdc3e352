name: Server

on:
  repository_dispatch:
    types: [frappe-framework-change]
  pull_request:
  workflow_dispatch:
  schedule:
    # Run everday at midnight UTC / 5:30 IST
    - cron: "0 0 * * *"

concurrency:
  group: server-${{ github.event_name }}-${{ github.event.number }}
  cancel-in-progress: true

permissions:
  # Do not change this as GITHUB_TOKEN is being used by roulette
  contents: read

jobs:
  typecheck:
    name: Types
    uses: ./.github/workflows/_base-type-check.yml

  checkrun:
    name: Plan Tests
    runs-on: ubuntu-latest
    needs: typecheck
    outputs:
      build: ${{ steps.check-build.outputs.build }}
    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v4
      - name: Check if unit tests should be run
        id: check-build
        run: |
          python "${GITHUB_WORKSPACE}/.github/helper/roulette.py"
        env:
          TYPE: "server"
          PR_NUMBER: ${{ github.event.number }}
          REPO_NAME: ${{ github.repository }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  test:
    name: Tests
    uses: ./.github/workflows/_base-server-tests.yml
    with:
      enable-sqlite: false  # This will test against both MariaDB and SQLite if enabled
      parallel-runs: 2
      enable-coverage: ${{ github.event_name != 'pull_request' }}
      fake-success: ${{ needs.checkrun.outputs.build != 'strawberry' }}
    needs: checkrun
    secrets: inherit

  migrate:
    name: Migration
    needs: checkrun
    uses: ./.github/workflows/_base-migration.yml
    with:
      db-artifact-url: https://frappeframework.com/files/v13-frappe.sql.gz
      python-version: '3.10'
      node-version: 22
      fake-success: ${{ needs.checkrun.outputs.build != 'strawberry' }}

  coverage:
    name: Coverage Wrap Up
    needs: [test, checkrun]
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'pull_request' }}
    steps:
      - name: Clone
        uses: actions/checkout@v4
      - name: Download artifacts
        uses: actions/download-artifact@v4.3.0
      - name: Upload coverage data
        uses: codecov/codecov-action@v5
        with:
          name: Server
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
          verbose: true
          flags: server

  dispatch:
    name: Downstream
    runs-on: "ubuntu-latest"
    needs: [test, migrate]
    if: ${{ contains( github.event.pull_request.labels.*.name, 'trigger-downstream-ci') }}
    strategy:
      matrix:
        repo:
          - frappe/erpnext
          - frappe/lending
          - frappe/hrms
    steps:
      - name: Dispatch Downstream CI (if supported)
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.CI_PAT }}
          repository: ${{ matrix.repo }}
          event-type: frappe-framework-change
          client-payload: '{"frappe_sha": "${{ github.sha }}"}'

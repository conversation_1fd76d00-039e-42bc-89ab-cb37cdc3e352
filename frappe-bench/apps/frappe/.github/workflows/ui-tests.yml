name: UI

on:
  pull_request:
  repository_dispatch:
    types: [frappe-framework-change]
  workflow_dispatch:
  schedule:
    # Run everday at midnight UTC / 5:30 IST
    - cron: "0 0 * * *"

concurrency:
  group: ui-develop-${{ github.event_name }}-${{ github.event.number }}
  cancel-in-progress: true

permissions:
  # Do not change this as GITHUB_TOKEN is being used by roulette
  contents: read

jobs:
  checkrun:
    name: Plan Tests
    runs-on: ubuntu-latest

    outputs:
      build: ${{ steps.check-build.outputs.build }}

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Check if build should be run
        id: check-build
        run: |
          python "${GITHUB_WORKSPACE}/.github/helper/roulette.py"
        env:
          TYPE: "ui"
          PR_NUMBER: ${{ github.event.number }}
          REPO_NAME: ${{ github.repository }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  test:
    name: Tests (Cypress)
    uses: ./.github/workflows/_base-ui-tests.yml
    with:
      parallel-runs: 3
      enable-coverage: ${{ github.event_name != 'pull_request' }}
      fake-success: ${{ needs.checkrun.outputs.build != 'strawberry' }}
    needs: checkrun

  coverage:
    name: Coverage Wrap Up
    needs: [test, checkrun]
    if: ${{ github.event_name != 'pull_request' }}
    runs-on: ubuntu-latest
    steps:
      - name: Clone
        uses: actions/checkout@v4
      - name: Download artifacts
        uses: actions/download-artifact@v4.3.0
      - name: Upload python coverage data
        uses: codecov/codecov-action@v5
        with:
          name: UIBackend
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
          verbose: true
          exclude: coverage-js*
          flags: server-ui
      - name: Upload JS coverage data
        uses: codecov/codecov-action@v5
        with:
          name: Cypress
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
          exclude: coverage-py*
          verbose: true
          flags: ui-tests

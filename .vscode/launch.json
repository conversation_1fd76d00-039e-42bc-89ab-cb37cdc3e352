{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Bench Command",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/frappe-bench/apps/frappe/frappe/utils/bench_helper.py",
      "args": [
        "frappe",
        "--site",
        "development.localhost",
        "clear-website-cache"
      ],
      "python": "${workspaceFolder}/frappe-bench/env/bin/python",
      "cwd": "${workspaceFolder}/frappe-bench/sites",
      "justMyCode": false,
      "env": {
        "DEV_SERVER": "0"
      }
    },
    {
      "name": "Bench Web",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/frappe-bench/apps/frappe/frappe/utils/bench_helper.py",
      "args": [
        "frappe",
        "serve",
        "--port",
        "8000",
        "--noreload",
        "--nothreading"
      ],
      "python": "${workspaceFolder}/frappe-bench/env/bin/python",
      "cwd": "${workspaceFolder}/frappe-bench/sites",
      "env": {
        "DEV_SERVER": "0"
      }
    },
    {
      "name": "Bench Short Worker",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/frappe-bench/apps/frappe/frappe/utils/bench_helper.py",
      "args": [
        "frappe",
        "worker",
        "--queue",
        "short"
      ],
      "python": "${workspaceFolder}/frappe-bench/env/bin/python",
      "cwd": "${workspaceFolder}/frappe-bench/sites",
      "env": {
        "DEV_SERVER": "1"
      }
    },
    {
      "name": "Bench Long Worker",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/frappe-bench/apps/frappe/frappe/utils/bench_helper.py",
      "args": [
        "frappe",
        "worker",
        "--queue",
        "long"
      ],
      "python": "${workspaceFolder}/frappe-bench/env/bin/python",
      "cwd": "${workspaceFolder}/frappe-bench/sites",
      "env": {
        "DEV_SERVER": "1"
      }
    },
    {
      "name": "Honcho SocketIO Watch Schedule Worker",
      "type": "debugpy",
      "request": "launch",
      "program": "/home/<USER>/.local/bin/honcho",
      "cwd": "${workspaceFolder}/frappe-bench",
      "console": "externalTerminal",
      "args": [
        "web",
        "socketio",
        "watch",
        "schedule",
        "worker"
      ]
    }
  ]
}